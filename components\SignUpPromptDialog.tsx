import React from 'react';
import { StyleSheet, View } from 'react-native';
import { Button, Dialog, Text, Portal, useTheme } from 'react-native-paper';
import { useNavigation } from '@react-navigation/native';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import AccountBenefitsList from './AccountBenefitsList';

type SignUpPromptDialogProps = {
    visible: boolean;
    onDismiss: () => void;
};

const SignUpPromptDialog: React.FC<SignUpPromptDialogProps> = ({ visible, onDismiss }) => {
    const theme = useTheme();
    const navigation = useNavigation<NativeStackNavigationProp<any>>();

    const handleSignUp = () => {
        onDismiss();
        navigation.navigate("AuthSignUpForm");
    };

    const handleGuestContinue = () => {
        onDismiss();
    };

    return (
        <Portal>
            <Dialog visible={visible} onDismiss={onDismiss} style={styles.dialog}>
                <Dialog.Icon
                    icon="account-plus"
                    size={60}
                    color={theme.colors.primary}
                />
                <Dialog.Title style={styles.title}>
                    Débloquez plus de fonctionnalités
                </Dialog.Title>
                <Dialog.Content style={styles.content}>
                    <Text variant="bodyLarge" style={styles.description}>
                        Créez votre compte gratuitement et sans engagement.
                    </Text>
                    <View style={styles.benefitsContainer}>
                        <AccountBenefitsList
                            variant="signup"
                            itemStyle={styles.benefitItem}
                        />
                    </View>
                </Dialog.Content>
                <Dialog.Actions style={styles.actions}>
                    <Button
                        mode="text"
                        onPress={handleGuestContinue}
                        style={styles.guestButton}
                    >
                        Continuer en mode invité
                    </Button>
                    <Button
                        mode="contained"
                        onPress={handleSignUp}
                        style={styles.createAccountButton}
                    >
                        Créer un compte
                    </Button>
                </Dialog.Actions>
            </Dialog>
        </Portal>
    );
};

const styles = StyleSheet.create({
    dialog: {
    },
    content: {
        alignItems: 'center',
    },
    title: {
        textAlign: 'center',
        fontWeight: 'bold',
    },
    description: {
        textAlign: 'center',
        marginBottom: 12,
    },
    benefitsContainer: {
        width: '100%',
    },
    benefitItem: {
        paddingVertical: 0,
    },
    actions: {
    },
    createAccountButton: {
    },
    guestButton: {
    },
});

export default SignUpPromptDialog;
