import React from 'react';
import { View, StyleSheet } from 'react-native';
import { List, useTheme } from 'react-native-paper';

interface Benefit {
    title: string;
    icon: string;
}

interface AccountBenefitsListProps {
    variant?: 'signup' | 'guest';
    titleStyle?: any;
    itemStyle?: any;
}

const BENEFITS: Record<string, Benefit[]> = {
    signup: [
        { title: "Jusqu'à 150 recherches/jour", icon: "magnify-plus" },
        { title: "Ajouter des pharmacies", icon: "map-marker-plus" },
        { title: "Sauvegarder vos chaînages", icon: "content-save" },
    ],
    guest: [
        { title: "Jusqu'à 150 recherches/jour", icon: "magnify-plus" },
        { title: "Ajouter des pharmacies", icon: "map-marker-plus" },
        { title: "Sauvegarder vos chaînages", icon: "content-save" },
    ]
};

const AccountBenefitsList: React.FC<AccountBenefitsListProps> = ({ 
    variant = 'signup', 
    titleStyle,
    itemStyle 
}) => {
    const theme = useTheme();
    const benefits = BENEFITS[variant];

    return (
        <View style={styles.container}>
            {benefits.map((benefit, index) => (
                <List.Item
                    key={index}
                    title={benefit.title}
                    left={() => <List.Icon icon={benefit.icon} color={theme.colors.primary} />}
                    titleStyle={titleStyle}
                    style={itemStyle}
                />
            ))}
        </View>
    );
};

const styles = StyleSheet.create({
    container: {
        width: '100%',
    },
});

export default AccountBenefitsList;
