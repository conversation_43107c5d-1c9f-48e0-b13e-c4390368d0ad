import { StackScreenProps } from '@react-navigation/stack';
import { Auth, getAuth, User, UserInfo, sendEmailVerification } from 'firebase/auth';
import React, { useContext, useState, useRef, useEffect } from 'react';
import { Alert, StyleSheet, View } from 'react-native';
import { Button, Text, Snackbar, ActivityIndicator, useTheme } from 'react-native-paper';
import { authSelectors, signOutUser } from '../../services/authService';
import useGlobalStore from '../../services/globalState';
import { apiServiceContext, translationServiceContext } from '../../services/provider';
import { TranslationService } from '../../services/translationService';
import { NativeStackScreenProps } from '@react-navigation/native-stack';
import { ApiService } from '../../services/api/apiService';
import { UserDetails } from '../../services/api/models/userDetails';
import { MaterialCommunityIcons } from '@expo/vector-icons';
import AsyncStorage from '@react-native-async-storage/async-storage';

type EmailVerificationScreenProps = NativeStackScreenProps<VerifiedUserStackParamList, 'EmailVerification'>;

const EmailVerificationScreen: React.FC<EmailVerificationScreenProps> = () => {

    const apiService: ApiService = useContext<ApiService>(apiServiceContext);
    const translationService: TranslationService = useContext<TranslationService>(translationServiceContext);
    const theme = useTheme();

    const auth: Auth = getAuth();

    const setFirebaseUser = useGlobalStore((state) => state.setFirebaseUser);
    const updateUserDetails = useGlobalStore((state) => state.update);
    const firebaseUser: User | null = useGlobalStore(authSelectors.firebaseUser);

    const email = firebaseUser?.email || '';

    // State for UI feedback
    const [isChecking, setIsChecking] = useState(false);
    const [isResending, setIsResending] = useState(false);
    const [snackbarVisible, setSnackbarVisible] = useState(false);
    const [snackbarMessage, setSnackbarMessage] = useState('');
    const [snackbarType, setSnackbarType] = useState<'success' | 'error'>('success');
    const [resendCooldown, setResendCooldown] = useState(0);

    const cooldownTimerRef = useRef<NodeJS.Timeout | null>(null);

    // Cleanup timer on unmount
    useEffect(() => {
        return () => {
            if (cooldownTimerRef.current) {
                clearInterval(cooldownTimerRef.current);
            }
        };
    }, []);

    // Start cooldown timer
    const startResendCooldown = () => {
        const COOLDOWN_SECONDS = 60; // 1 minute cooldown
        setResendCooldown(COOLDOWN_SECONDS);

        cooldownTimerRef.current = setInterval(() => {
            setResendCooldown((prev) => {
                if (prev <= 1) {
                    if (cooldownTimerRef.current) {
                        clearInterval(cooldownTimerRef.current);
                        cooldownTimerRef.current = null;
                    }
                    return 0;
                }
                return prev - 1;
            });
        }, 1000);
    };

    const fetchAndUpdateUserDetails = async (userUid: string): Promise<void> => {
        try {
            const response = await apiService.getUserDetails(userUid);

            if (!response.ok) {
                const errorText = await response.text();
                console.error("Failed to fetch user details:", response.status, errorText);
                throw new Error(errorText || "Failed to fetch user details");
            }

            const userDetails: UserDetails = await response.json();
            updateUserDetails(userDetails);
        } catch (error) {
            console.error("Error in fetchAndUpdateUserDetails:", error);
            throw error;
        }
    };

    const showSnackbar = (message: string, type: 'success' | 'error' = 'success') => {
        setSnackbarMessage(message);
        setSnackbarType(type);
        setSnackbarVisible(true);
    };

    const reloadUser = async (): Promise<User | null> => {
        await firebaseUser?.reload();
        await firebaseUser?.getIdToken(true);
        const reloadedUser: User | null = auth.currentUser;
        if (reloadedUser?.emailVerified) {
            setFirebaseUser(reloadedUser ? { ...reloadedUser } as User : null);
            fetchAndUpdateUserDetails(reloadedUser.uid);
        }
        return reloadedUser;
    };

    const handleCheckVerification = async () => {
        if (!firebaseUser) return;
        
        setIsChecking(true);
        try {
            const reloadedUser = await reloadUser();
            if (reloadedUser?.emailVerified) {
                showSnackbar(translationService.translate("EMAIL_VERIFICATION_SUCCESS"), 'success');
                console.log("Email verified! Proceeding...");
                // Navigation will happen automatically due to the auth state change
            } else {
                showSnackbar(translationService.translate("EMAIL_VERIFICATION_STILL_PENDING"), 'error');
                console.log("Email still not verified.");
            }
        } catch (error) {
            console.error("Error during verification check:", error);
            showSnackbar(translationService.translate("EMAIL_VERIFICATION_ERROR"), 'error');
        } finally {
            setIsChecking(false);
        }
    };

    const handleResendEmail = async () => {
        if (!firebaseUser) return;
        if (resendCooldown > 0) return; // Prevent resend during cooldown

        setIsResending(true);
        try {
            await sendEmailVerification(firebaseUser);
            showSnackbar(translationService.translate("EMAIL_VERIFICATION_RESEND_SUCCESS"), 'success');
            startResendCooldown(); // Start cooldown after successful send
        } catch (error) {
            console.error("Error resending verification email:", error);
            showSnackbar(translationService.translate("EMAIL_VERIFICATION_RESEND_ERROR"), 'error');
        } finally {
            setIsResending(false);
        }
    };

    const handleWrongEmail = () => {
        Alert.alert(
            translationService.translate("EMAIL_VERIFICATION_WRONG_EMAIL_BUTTON"),
            translationService.translate("EMAIL_VERIFICATION_CHANGE_EMAIL_DIALOG_CONTENT"),
            [
                {
                    text: translationService.translate("CANCEL"),
                    style: "cancel"
                },
                {
                    text: translationService.translate("OK"),
                    onPress: async () => {
                        await signOutUser();
                        // Navigation will happen automatically due to auth state change
                    }
                }
            ]
        );
    };

    return (
        <View style={styles.container}>
            <View style={styles.iconContainer}>
                <MaterialCommunityIcons
                    name="email-check-outline"
                    size={80}
                    color={theme.colors.primary}
                />
            </View>

            <Text variant="headlineSmall" style={styles.title}>
                {translationService.translate("EMAIL_VERIFICATION_TITLE")}
            </Text>

            <Text variant="bodyLarge" style={styles.message}>
                {translationService.translate("EMAIL_VERIFICATION_MESSAGE")}
            </Text>

            <View style={[styles.emailContainer, { backgroundColor: theme.colors.surfaceVariant }]}>
                <MaterialCommunityIcons
                    name="email"
                    size={20}
                    color={theme.colors.onSurfaceVariant}
                    style={styles.emailIcon}
                />
                <Text variant="bodyMedium" style={[styles.email, { color: theme.colors.primary }]}>
                    {email}
                </Text>
            </View>

            <Text variant="bodyMedium" style={styles.instructions}>
                {translationService.translate("EMAIL_VERIFICATION_INSTRUCTIONS")}
            </Text>

            <View style={styles.buttonContainer}>
                <Button
                    mode="contained"
                    style={[styles.button, styles.primaryButton]}
                    onPress={handleCheckVerification}
                    disabled={isChecking}
                    loading={isChecking}
                >
                    {isChecking 
                        ? translationService.translate("EMAIL_VERIFICATION_CHECKING")
                        : translationService.translate("EMAIL_VERIFICATION_CHECK_BUTTON")
                    }
                </Button>

                <Button
                    mode="outlined"
                    style={[styles.button, styles.secondaryButton]}
                    onPress={handleResendEmail}
                    disabled={isResending || resendCooldown > 0}
                    icon={isResending ? undefined : "email-sync"}
                    loading={isResending}
                >
                    {resendCooldown > 0
                        ? `${translationService.translate("EMAIL_VERIFICATION_RESEND_BUTTON")} (${resendCooldown}s)`
                        : translationService.translate("EMAIL_VERIFICATION_RESEND_BUTTON")
                    }
                </Button>

                <Button
                    mode="text"
                    style={[styles.button, styles.textButton]}
                    onPress={handleWrongEmail}
                    icon="account-edit"
                    textColor={theme.colors.onSurfaceVariant}
                >
                    {translationService.translate("EMAIL_VERIFICATION_WRONG_EMAIL_BUTTON")}
                </Button>
            </View>

            <Snackbar
                visible={snackbarVisible}
                onDismiss={() => setSnackbarVisible(false)}
                duration={4000}
                style={[
                    styles.snackbar,
                    snackbarType === 'error' && { backgroundColor: theme.colors.error }
                ]}
                action={{
                    label: translationService.translate("OK"),
                    onPress: () => setSnackbarVisible(false),
                }}
            >
                {snackbarMessage}
            </Snackbar>
        </View>
    );
}

const styles = StyleSheet.create({
    container: {
        flex: 1,
        justifyContent: 'center',
        alignItems: 'center',
        padding: 24,
    },
    iconContainer: {
        alignItems: 'center',
        marginBottom: 32,
    },
    title: {
        textAlign: 'center',
        marginBottom: 16,
        fontWeight: 'bold',
    },
    message: {
        textAlign: 'center',
        marginBottom: 24,
        lineHeight: 24,
    },
    emailContainer: {
        flexDirection: 'row',
        alignItems: 'center',
        padding: 16,
        marginBottom: 20,
        borderRadius: 8,
        minWidth: 280,
    },
    emailIcon: {
        marginRight: 12,
    },
    email: {
        flex: 1,
        fontWeight: '600',
        fontSize: 16,
    },
    instructions: {
        textAlign: 'center',
        lineHeight: 20,
        marginBottom: 32,
    },
    buttonContainer: {
        alignItems: 'center',
        gap: 16,
    },
    button: {
        minWidth: 200,
        paddingVertical: 4,
    },
    primaryButton: {
        // Uses default theme colors
    },
    secondaryButton: {
        // Uses default theme colors
    },
    textButton: {
        marginTop: 8,
    },
    snackbar: {
        marginBottom: 16,
    },
});

export default EmailVerificationScreen;
